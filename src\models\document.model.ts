import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {DocumentUpdate} from './document-update.model';
import {DropdownItems} from './dropdown-items.model';
import {User} from './user.model';

@model()
export class Document extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  docId?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  scopeApplicability?: string;

  @property({
    type: 'string',
  })
  purpose?: string;

  @property({
    type: 'string',
  })
  keywords?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  uniqueid?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  files?: string;

  @property({
    type: 'string',
  })
  status?: 'Inititated' | 'In Progress' | 'Completed' | 'Archived' | 'Returned by Curator' | 'Returned by Approver'
    | 'Sent to Curator' | 'In Review with Approver' | 'Approved' | 'In Review with Reviewer' | 'Returned';

  @property({
    type: 'any',
  })
  value?: any;


  @property({
    type: 'string',
  })
  type?: 'Existing' | 'New';

  @property({
    type: 'string',
  })
  docStatus?: 'In Draft' | 'Published' | 'Archived';

  @property({
    type: 'string',
  })
  maskId?: string;


  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  creatorTargetDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  reviewerTargetDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  approverTargetDate?: string;



  @hasMany(() => DocumentUpdate)
  documentUpdates: DocumentUpdate[];

  @belongsTo(() => User)
  initiatorId: string;

  @belongsTo(() => User)
  creatorId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  approverId: string;

  @belongsTo(() => DropdownItems)
  documentCategoryId: string;

  constructor(data?: Partial<Document>) {
    super(data);
  }
}

export interface DocumentRelations {
  // describe navigational properties here
}

export type DocumentWithRelations = Document & DocumentRelations;
