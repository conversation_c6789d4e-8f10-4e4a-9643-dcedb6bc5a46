import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {Document, DocumentRelations, DocumentUpdate, DropdownItems, User} from '../models';
import {DocumentUpdateRepository} from './document-update.repository';
import {DropdownItemsRepository} from './dropdown-items.repository';
import {UserRepository} from './user.repository';

export class DocumentRepository extends DefaultCrudRepository<
  Document,
  typeof Document.prototype.id,
  DocumentRelations
> {

  public readonly documentUpdates: HasManyRepositoryFactory<DocumentUpdate, typeof Document.prototype.id>;

  public readonly initiator: BelongsToAccessor<User, typeof Document.prototype.id>;

  public readonly creator: <PERSON>ongsToAccessor<User, typeof Document.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof Document.prototype.id>;

  public readonly approver: BelongsToAccessor<User, typeof Document.prototype.id>;

  public readonly documentCategory: BelongsToAccessor<DropdownItems, typeof Document.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('DocumentUpdateRepository') protected documentUpdateRepositoryGetter: Getter<DocumentUpdateRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('DropdownItemsRepository') protected dropdownItemsRepositoryGetter: Getter<DropdownItemsRepository>,
  ) {
    super(Document, dataSource);
    this.documentCategory = this.createBelongsToAccessorFor('documentCategory', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('documentCategory', this.documentCategory.inclusionResolver);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.creator = this.createBelongsToAccessorFor('creator', userRepositoryGetter,);
    this.registerInclusionResolver('creator', this.creator.inclusionResolver);
    this.initiator = this.createBelongsToAccessorFor('initiator', userRepositoryGetter,);
    this.registerInclusionResolver('initiator', this.initiator.inclusionResolver);
    this.documentUpdates = this.createHasManyRepositoryFactoryFor('documentUpdates', documentUpdateRepositoryGetter,);
    this.registerInclusionResolver('documentUpdates', this.documentUpdates.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
