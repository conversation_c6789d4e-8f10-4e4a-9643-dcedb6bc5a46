import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Document,
  User,
} from '../models';
import {DocumentRepository} from '../repositories';

export class DocumentUserController {
  constructor(
    @repository(DocumentRepository)
    public documentRepository: DocumentRepository,
  ) { }

  @get('/documents/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Document',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Document.prototype.id,
  ): Promise<User> {
    return this.documentRepository.initiator(id);
  }
}
